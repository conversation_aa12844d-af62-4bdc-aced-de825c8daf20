# Token 使用示例

## 1. 登录并存储Token

```typescript
import { useTokenStore } from '@/store/token'
import { userWxLogin } from '@/api/login'

// 在组件中使用
const tokenStore = useTokenStore()

// 登录示例
async function handleLogin() {
  try {
    const loginData = {
      code: "0e3BoZ00059hVU1gcn400meK280BoZ0n",
      encryptedData: "yLGYzK4lB1czPOP5eFlR/adeb0/wdJiU394rInJ/yjxazYnUwFSpgG0c8XP2uOkyuT5ECZ8AnQfi7RZZrnJrrSVMwQZgWJfN+0VbtA9N3L+hTD72nlFIQlsDvxnAX7FLQDPqJzlurDhRDVoDGotfMG5CCCh+4Yav/MaeyxPhzotnZeeTJa/DFrgCNh2cOeY6y6ZsQVXlB0NYOD0LcYVI/A==",
      iv: "3vQYrVkw77i9FdE1vcsf1A==",
      nickName: "微信用户",
      avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
      gender: "0"
    }

    // 调用登录API
    const response = await userWxLogin(loginData)
    
    // 处理登录响应并存储token
    const result = await tokenStore.handleLoginResponse(response)
    
    if (result.success) {
      console.log('登录成功，Token:', result.token)
      uni.showToast({ title: '登录成功', icon: 'success' })
    }
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({ title: error.message || '登录失败', icon: 'none' })
  }
}
```

## 2. 获取Token

```typescript
import { useTokenStore } from '@/store/token'

const tokenStore = useTokenStore()

// 获取当前token
const currentToken = tokenStore.getToken
console.log('当前Token:', currentToken)

// 检查是否已登录
const isLoggedIn = tokenStore.hasLogin
console.log('是否已登录:', isLoggedIn)
```

## 3. 在API请求中使用Token

Token会自动通过HTTP拦截器添加到请求头中：

```typescript
// 在 src/http/interceptor.ts 中，token会自动添加
// 3. 添加 token 请求头标识
const tokenStore = useTokenStore()
const token = tokenStore.getToken
if (token) {
  options.header.Authorization = `Bearer ${token}`
}
```

## 4. 退出登录

```typescript
import { useTokenStore } from '@/store/token'

const tokenStore = useTokenStore()

// 退出登录
async function handleLogout() {
  try {
    await tokenStore.logout()
    uni.showToast({ title: '退出成功', icon: 'success' })
    // 跳转到登录页
    uni.navigateTo({ url: '/pages/login/login' })
  } catch (error) {
    console.error('退出失败:', error)
  }
}
```

## 5. 在页面中监听登录状态

```vue
<template>
  <view>
    <view v-if="tokenStore.hasLogin">
      <text>已登录，Token: {{ tokenStore.getToken.substring(0, 20) }}...</text>
      <button @click="handleLogout">退出登录</button>
    </view>
    <view v-else>
      <text>未登录</text>
      <button @click="handleLogin">去登录</button>
    </view>
  </view>
</template>

<script setup>
import { useTokenStore } from '@/store/token'

const tokenStore = useTokenStore()

function handleLogin() {
  uni.navigateTo({ url: '/pages/login/login' })
}

async function handleLogout() {
  await tokenStore.logout()
}
</script>
```

## 6. API响应数据结构

登录API返回的数据结构：
```json
{
  "code": 200,
  "msg": "操作成功", 
  "data": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxODIwMjY3MTg2MSIsImxvZ2luX3VzZXJfa2V5IjoiODRiNzY0YTgtZmM1Ni00NzdjLWE3MDAtYTU1NmFlZDViNTI4In0.xrFr4fCuwqXqv4yhzQ_JPbeIMFJIEqj-Zba_egS5cTYISeH44XGTDZbUeSVHUJn6B6sn5Rv0xhk0NUtPs2a1Sw"
}
```

其中 `data` 字段就是JWT token，会被自动存储到本地并在后续请求中使用。