<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "个人信息"
  }
}
</route>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'

interface ChooseAvatarEvent {
  detail: {
    avatarUrl: string
  }
}

interface NicknameInputEvent {
  detail: {
    value: string
  }
}

const userInfo = reactive({
  avatar: '/static/images/default-avatar.png',
  nickname: '微信用户',
})

const userAvatar = ref(userInfo.avatar)
const tempNickname = ref(userInfo.nickname)

onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.avatar) {
    const decodedAvatar = decodeURIComponent(options.avatar)
    userAvatar.value = decodedAvatar
    userInfo.avatar = decodedAvatar
  }
  if (options.nickname) {
    const decodedNickname = decodeURIComponent(options.nickname)
    tempNickname.value = decodedNickname
    userInfo.nickname = decodedNickname
  }
})

function onChooseAvatar(e: ChooseAvatarEvent) {
  userAvatar.value = e.detail.avatarUrl
}

function onNicknameInput(e: NicknameInputEvent) {
  tempNickname.value = e.detail.value
}

function handleSave() {
  const nickname = tempNickname.value.trim()
  if (!nickname) {
    uni.showToast({ title: '请输入昵称', icon: 'none' })
    return
  }

  userInfo.nickname = nickname
  userInfo.avatar = userAvatar.value

  uni.showToast({ title: '保存成功', icon: 'success' })

  setTimeout(() => {
    const eventChannel = (uni as any).getOpenerEventChannel?.()
    eventChannel?.emit('updateUserInfo', {
      avatar: userInfo.avatar,
      nickname: userInfo.nickname,
    })
    uni.navigateBack()
  }, 1000)
}
</script>

<template>
  <view class="min-h-screen flex flex-col bg-gray-100">
    <view class="flex-1 p-6">
      <!-- 头像区域 -->
      <view class="mb-10 flex justify-center">
        <button
          class="relative h-[150rpx] w-[150rpx] center rounded-full bg-transparent p-0 after:border-none"
          open-type="chooseAvatar"
          @chooseavatar="onChooseAvatar"
        >
          <image
            class="h-full w-full border-4 border-white rounded-full bg-gray-200 shadow-lg"
            :src="userAvatar"
            mode="aspectFill"
          />
        </button>
      </view>

      <view class="rounded-xl bg-white p-4 shadow-sm">
        <view class="flex items-center py-3">
          <view class="w-20 text-base text-gray-700">
            昵称
          </view>
          <view class="flex-1">
            <input
              v-model="tempNickname"
              type="nickname"
              class="text-right text-base text-gray-800 placeholder-gray-400"
              placeholder="请输入昵称"
              @input="onNicknameInput"
            >
          </view>
        </view>
      </view>

      <view class="mt-12 px-4">
        <button
          class="h-12 w-full center rounded-full text-lg text-white font-medium shadow-md transition-transform active:scale-95"
          style="background: linear-gradient(135deg, #4ade80 0%, #06b6d4 100%)"
          @click="handleSave"
        >
          保存
        </button>
      </view>
    </view>
  </view>
</template>

<style scoped>
input {
  border: none;
  background-color: transparent;
  padding: 0;
  outline: none;
}
</style>
