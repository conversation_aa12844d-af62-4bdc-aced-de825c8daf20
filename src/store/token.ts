import type { IAuthLoginRes } from '../api/types/login'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { logout as _logout } from '../api/login'
import { useUserStore } from './user'

// 初始化状态
const tokenInfoState = {
  Token: '',
}

export const useTokenStore = defineStore(
  'token',
  () => {
    // 定义token信息
    const tokenInfo = ref({ ...tokenInfoState })

    // 设置token信息
    const setTokenInfo = (val: { Token: string }) => {
      tokenInfo.value = val
    }

    /**
     * 登录成功后处理逻辑
     * @param token 登录返回的token字符串
     */
    const postLogin = async (token: string) => {
      setTokenInfo({ Token: token })
      const userStore = useUserStore()
      await userStore.fetchUserInfo()
    }

    /**
     * 处理登录API响应
     * @param loginResponse 登录API的完整响应
     */
    const handleLoginResponse = async (loginResponse: IResData<IAuthLoginRes>) => {
      if (loginResponse.code === 200) {
        await postLogin(loginResponse.data.data)
        return { success: true, token: loginResponse.data.data }
      }
      else {
        throw new Error(loginResponse.msg || '登录失败')
      }
    }

    /**
     * 用户登录
     * @param credentials 登录参数
     * @returns 登录结果
     */

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      try {
        await _logout()
      }
      catch (error) {
        console.error('退出登录失败:', error)
      }
      finally {
        // 无论成功失败，都需要清除本地token信息
        tokenInfo.value = { ...tokenInfoState }
        const userStore = useUserStore()
        await userStore.removeUserInfo()
      }
    }

    /**
     * 获取token
     */
    const getToken = computed(() => {
      return tokenInfo.value.Token
    })

    /**
     * 检查是否已登录
     */
    const hasLogin = computed(() => {
      return !!tokenInfo.value.Token
    })

    return {
      // 核心API方法
      logout,
      postLogin,
      handleLoginResponse,

      // 认证状态判断
      hasLogin,

      // token获取
      getToken,

      // 调试或特殊场景可能需要直接访问的信息
      tokenInfo,
    }
  },
  {
    // 添加持久化配置，确保刷新页面后token信息不丢失
    persist: {
      key: 'token', // 存储的key
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      },
      // 指定需要持久化的状态
      paths: ['tokenInfo'],
    },
  },
)
