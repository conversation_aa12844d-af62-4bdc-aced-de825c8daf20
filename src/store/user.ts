import type { IUserInfoRes } from '@/api/types/login'
import { defineStore } from 'pinia'
import { ref } from 'vue'
// import { getUserInfo } from '@/api/login' // 暂时注释，需要后端提供用户信息API

// 初始化状态
const userInfoState: IUserInfoRes = {
  userId: 0,
  username: '',
  nickname: '',
  avatar: '/static/images/default-avatar.png',
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 定义用户信息
    const userInfo = ref<IUserInfoRes>({ ...userInfoState })
    // 设置用户信息
    const setUserInfo = (val: IUserInfoRes) => {
      console.log('设置用户信息', val)
      // 若头像为空 则使用默认头像
      if (!val.avatar) {
        val.avatar = userInfoState.avatar
      }
      userInfo.value = val
    }
    const setUserAvatar = (avatar: string) => {
      userInfo.value.avatar = avatar
      console.log('设置用户头像', avatar)
      console.log('userInfo', userInfo.value)
    }
    // 删除用户信息
    const removeUserInfo = async () => {
      userInfo.value = { ...userInfoState }
      uni.removeStorageSync('user')
    }

    /**
     * 获取用户信息
     */
    const fetchUserInfo = async () => {
      try {
        const mockUserInfo: IUserInfoRes = {
          userId: 1,
          username: 'user',
          nickname: userInfo.value.nickname || '用户',
          avatar: userInfo.value.avatar || userInfoState.avatar,
        }
        setUserInfo(mockUserInfo)
        return mockUserInfo
      }
      catch (error) {
        console.error('获取用户信息失败:', error)
        throw error
      }
    }

    return {
      userInfo,
      removeUserInfo,
      fetchUserInfo,
      setUserInfo,
      setUserAvatar,
    }
  },
  {
    persist: {
      key: 'user', // 存储的key
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
        removeItem: (key: string) => uni.removeStorageSync(key),
      },
      // 指定需要持久化的状态
      paths: ['userInfo'],
    },
  },
)
