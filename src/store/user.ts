import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 用户信息接口
export interface IUserInfo {
  nickName: string
  avatarUrl: string
  gender: string
  userWxToken: string
}

// 默认用户信息
const defaultUserInfo: IUserInfo = {
  nickName: '微信用户',
  avatarUrl: '/static/images/default-avatar.png', // 灰色头像
  gender: '0',
  userWxToken: '',
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 用户信息状态
    const userInfo = ref<IUserInfo>({ ...defaultUserInfo })

    /**
     * 设置用户信息
     * @param info 用户信息
     */
    const setUserInfo = (info: Partial<IUserInfo>) => {
      userInfo.value = { ...userInfo.value, ...info }
    }

    /**
     * 设置用户token
     * @param token 用户token
     */
    const setUserWxToken = (token: string) => {
      userInfo.value.userWxToken = token
    }

    /**
     * 获取用户token
     */
    const getUserWxToken = computed(() => {
      return userInfo.value.userWxToken
    })

    /**
     * 更新用户昵称和头像（微信小程序手动获取时调用）
     * @param nickName 昵称
     * @param avatarUrl 头像URL
     */
    const updateUserProfile = (nickName: string, avatarUrl: string) => {
      userInfo.value.nickName = nickName
      userInfo.value.avatarUrl = avatarUrl
    }

    /**
     * 清空用户信息（退出登录时调用）
     */
    const clearUserInfo = () => {
      userInfo.value = { ...defaultUserInfo }
    }

    /**
     * 检查是否已登录
     */
    const isLoggedIn = computed(() => {
      return !!userInfo.value.userWxToken
    })

    return {
      // 状态
      userInfo,

      // 计算属性
      getUserWxToken,
      isLoggedIn,

      // 方法
      setUserInfo,
      setUserWxToken,
      updateUserProfile,
      clearUserInfo,
    }
  },
  {
    persist: {
      key: 'userInfo',
      storage: {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      },
      paths: ['userInfo'],
    },
  },
)
