/**
 * Token调试工具
 * 用于验证token的存储和读取功能
 */

import { useTokenStore } from '@/store/token'

export class TokenDebugger {
  private tokenStore = useTokenStore()

  /**
   * 检查token存储状态
   */
  checkTokenStatus() {
    const status = {
      storeToken: this.tokenStore.getToken,
      hasLogin: this.tokenStore.hasLogin,
      localStorageData: uni.getStorageSync('token'),
      timestamp: new Date().toISOString()
    }
    
    console.log('🔍 Token状态检查:', status)
    return status
  }

  /**
   * 模拟设置token
   */
  async mockSetToken() {
    const mockResponse = {
      code: 200,
      msg: "操作成功",
      data: "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxODIwMjY3MTg2MSIsImxvZ2luX3VzZXJfa2V5IjoiODRiNzY0YTgtZmM1Ni00NzdjLWE3MDAtYTU1NmFlZDViNTI4In0.xrFr4fCuwqXqv4yhzQ_JPbeIMFJIEqj-Zba_egS5cTYISeH44XGTDZbUeSVHUJn6B6sn5Rv0xhk0NUtPs2a1Sw"
    }

    try {
      await this.tokenStore.handleLoginResponse(mockResponse)
      console.log('✅ Mock token设置成功')
      return this.checkTokenStatus()
    } catch (error) {
      console.error('❌ Mock token设置失败:', error)
      throw error
    }
  }

  /**
   * 清除token
   */
  async clearToken() {
    try {
      await this.tokenStore.logout()
      console.log('✅ Token清除成功')
      return this.checkTokenStatus()
    } catch (error) {
      console.error('❌ Token清除失败:', error)
      throw error
    }
  }

  /**
   * 检查所有本地存储
   */
  checkAllStorage() {
    try {
      const storageInfo = uni.getStorageInfoSync()
      const allData: Record<string, any> = {}
      
      storageInfo.keys.forEach(key => {
        allData[key] = uni.getStorageSync(key)
      })
      
      console.log('📱 所有本地存储数据:', {
        keys: storageInfo.keys,
        currentSize: storageInfo.currentSize,
        limitSize: storageInfo.limitSize,
        data: allData
      })
      
      return allData
    } catch (error) {
      console.error('❌ 检查本地存储失败:', error)
      throw error
    }
  }

  /**
   * 验证token持久化
   */
  async testPersistence() {
    console.log('🧪 开始token持久化测试...')
    
    // 1. 设置token
    await this.mockSetToken()
    
    // 2. 检查状态
    const beforeStatus = this.checkTokenStatus()
    
    // 3. 模拟应用重启（清除内存中的store，但保留本地存储）
    console.log('🔄 模拟应用重启...')
    
    // 4. 重新检查状态
    setTimeout(() => {
      const afterStatus = this.checkTokenStatus()
      
      console.log('📊 持久化测试结果:', {
        before: beforeStatus,
        after: afterStatus,
        isPersisted: afterStatus.storeToken === beforeStatus.storeToken
      })
    }, 100)
  }
}

// 创建全局调试实例
export const tokenDebugger = new TokenDebugger()

// 在开发环境下将调试器挂载到全局
// #ifdef MP-WEIXIN
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  wx.tokenDebugger = tokenDebugger
}
// #endif

// #ifdef H5
if (process.env.NODE_ENV === 'development') {
  // @ts-ignore
  window.tokenDebugger = tokenDebugger
}
// #endif